package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// Database represents the database connection and operations
type Database struct {
	db     *gorm.DB
	config *config.Config
	logger *logrus.Logger
}

// New creates a new database instance with fallback to memory storage
func New(cfg *config.Config) (*Database, error) {
	logrusLogger := logrus.New()
	logrusLogger.SetFormatter(&logrus.JSONFormatter{})

	var db *gorm.DB
	var err error

	// Configure GORM logger
	gormLogger := gormlogger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		gormlogger.Config{
			SlowThreshold:             time.Second,       // Slow SQL threshold
			LogLevel:                  gormlogger.Silent, // Log level
			IgnoreRecordNotFoundError: true,              // Ignore ErrRecordNotFound error for logger
			Colorful:                  false,             // Disable color
		},
	)

	// Determine database type
	dbType := cfg.Database.Type
	if dbType == "" {
		dbType = "sqlite" // Default to SQLite
	}

	// Try to connect to database
	switch dbType {
	case "postgres", "postgresql":
		dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=UTC",
			cfg.Database.Host,
			cfg.Database.Username,
			cfg.Database.Password,
			cfg.Database.Database,
			cfg.Database.Port,
			cfg.Database.SSLMode,
		)
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
			Logger: gormLogger,
		})
	case "sqlite":
		dbPath := cfg.Database.Database
		if dbPath == "" {
			dbPath = "spt.db"
		}
		db, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{
			Logger: gormLogger,
		})
	default:
		return nil, fmt.Errorf("unsupported database type: %s", dbType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s database: %w", dbType, err)
	}

	database := &Database{
		db:     db,
		config: cfg,
		logger: logrusLogger,
	}

	// Run migrations
	if err := database.Migrate(); err != nil {
		return nil, fmt.Errorf("failed to run database migrations: %w", err)
	}

	logrusLogger.WithFields(logrus.Fields{
		"type": dbType,
		"host": cfg.Database.Host,
		"port": cfg.Database.Port,
		"db":   cfg.Database.Database,
	}).Info("Database connected successfully")

	return database, nil
}

// Migrate runs database migrations
func (d *Database) Migrate() error {
	if d.db == nil {
		return fmt.Errorf("database connection not available")
	}

	d.logger.Info("Running database migrations...")

	// Auto-migrate all models
	err := d.db.AutoMigrate(
		&models.User{},
		&models.SecurityIssue{},
		&models.ScanResult{},
		&models.WalletInfo{},
		&models.ContractInfo{},
		&models.DependencyInfo{},
		&models.SecurityReport{},
		&models.SecurityChecklist{},
		&models.ProjectConfig{},
	)

	if err != nil {
		return fmt.Errorf("failed to run auto-migration: %w", err)
	}

	d.logger.Info("Database migrations completed successfully")
	return nil
}

// Close closes the database connection
func (d *Database) Close() error {
	if d.db == nil {
		return nil
	}
	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB returns the underlying GORM database instance
func (d *Database) GetDB() *gorm.DB {
	return d.db
}

// Health checks database connectivity
func (d *Database) Health() error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// IsConnected returns true if database is connected
func (d *Database) IsConnected() bool {
	return d.db != nil
}

// ScanResult operations
func (d *Database) CreateScanResult(result *models.ScanResult) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Create(result).Error
}

func (d *Database) GetScanResult(id string) (*models.ScanResult, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var result models.ScanResult
	err := d.db.Preload("Issues").First(&result, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (d *Database) UpdateScanResult(result *models.ScanResult) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Save(result).Error
}

func (d *Database) GetScanHistory(limit int) ([]*models.ScanResult, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var results []*models.ScanResult
	query := d.db.Preload("Issues").Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&results).Error
	return results, err
}

func (d *Database) DeleteScanResult(id string) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Delete(&models.ScanResult{}, "id = ?", id).Error
}

// SecurityIssue operations
func (d *Database) CreateSecurityIssue(issue *models.SecurityIssue) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Create(issue).Error
}

func (d *Database) GetSecurityIssuesByType(issueType string) ([]*models.SecurityIssue, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var issues []*models.SecurityIssue
	err := d.db.Where("type = ?", issueType).Find(&issues).Error
	return issues, err
}

func (d *Database) GetSecurityIssuesBySeverity(severity string) ([]*models.SecurityIssue, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var issues []*models.SecurityIssue
	err := d.db.Where("severity = ?", severity).Find(&issues).Error
	return issues, err
}

// SecurityReport operations
func (d *Database) CreateSecurityReport(report *models.SecurityReport) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Create(report).Error
}

func (d *Database) GetSecurityReport(id string) (*models.SecurityReport, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var report models.SecurityReport
	err := d.db.Preload("ScanResult").First(&report, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

func (d *Database) GetReportsByScanID(scanID string) ([]*models.SecurityReport, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var reports []*models.SecurityReport
	err := d.db.Where("scan_result_id = ?", scanID).Find(&reports).Error
	return reports, err
}

// ProjectConfig operations
func (d *Database) CreateProjectConfig(config *models.ProjectConfig) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Create(config).Error
}

func (d *Database) GetProjectConfig(projectPath string) (*models.ProjectConfig, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var config models.ProjectConfig
	err := d.db.First(&config, "project_path = ?", projectPath).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (d *Database) UpdateProjectConfig(config *models.ProjectConfig) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Save(config).Error
}

// User operations
func (d *Database) CreateUser(user *models.User) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Create(user).Error
}

func (d *Database) GetUser(id string) (*models.User, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var user models.User
	err := d.db.First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *Database) GetUserByUsername(username string) (*models.User, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var user models.User
	err := d.db.First(&user, "username = ?", username).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *Database) GetUserByEmail(email string) (*models.User, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var user models.User
	err := d.db.First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *Database) UpdateUser(user *models.User) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Save(user).Error
}

func (d *Database) DeleteUser(id string) error {
	if d.db == nil {
		return fmt.Errorf("database not connected")
	}
	return d.db.Delete(&models.User{}, "id = ?", id).Error
}

func (d *Database) GetAllUsers() ([]*models.User, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	var users []*models.User
	err := d.db.Find(&users).Error
	return users, err
}

func (d *Database) UserExists(username string) (bool, error) {
	if d.db == nil {
		return false, fmt.Errorf("database not connected")
	}
	var count int64
	err := d.db.Model(&models.User{}).Where("username = ?", username).Count(&count).Error
	return count > 0, err
}

// Statistics and analytics
func (d *Database) GetScanStatistics() (map[string]interface{}, error) {
	if d.db == nil {
		return nil, fmt.Errorf("database not connected")
	}

	stats := make(map[string]interface{})

	// Total scans
	var totalScans int64
	d.db.Model(&models.ScanResult{}).Count(&totalScans)
	stats["total_scans"] = totalScans

	// Scans by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	d.db.Model(&models.ScanResult{}).Select("status, count(*) as count").Group("status").Scan(&statusCounts)
	stats["scans_by_status"] = statusCounts

	// Issues by severity
	var severityCounts []struct {
		Severity string
		Count    int64
	}
	d.db.Model(&models.SecurityIssue{}).Select("severity, count(*) as count").Group("severity").Scan(&severityCounts)
	stats["issues_by_severity"] = severityCounts

	// Issues by chain
	var chainCounts []struct {
		Chain string
		Count int64
	}
	d.db.Model(&models.SecurityIssue{}).Select("chain, count(*) as count").Group("chain").Scan(&chainCounts)
	stats["issues_by_chain"] = chainCounts

	// Recent activity (last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var recentScans int64
	d.db.Model(&models.ScanResult{}).Where("created_at > ?", thirtyDaysAgo).Count(&recentScans)
	stats["recent_scans"] = recentScans

	// Average scan duration
	var avgDuration struct {
		AvgSeconds float64
	}
	d.db.Model(&models.ScanResult{}).
		Select("AVG(EXTRACT(EPOCH FROM duration)) as avg_seconds").
		Where("duration IS NOT NULL").
		Scan(&avgDuration)
	stats["avg_scan_duration_seconds"] = avgDuration.AvgSeconds

	return stats, nil
}
