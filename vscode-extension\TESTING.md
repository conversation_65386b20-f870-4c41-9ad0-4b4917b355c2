# VSCode Extension Testing Guide

This guide covers testing strategies for the Blockchain Security Protocol (SPT) VSCode extension.

## Current Test Setup

The extension currently uses a simplified test setup that doesn't require external testing frameworks. This approach ensures the build process works without additional dependencies.

### Available Test Commands

```bash
# Run basic tests (currently disabled for CI/CD compatibility)
npm test

# Run manual testing (when full test framework is set up)
npm run test:manual

# Compile and lint (part of test process)
npm run pretest
```

## Manual Testing Checklist

### 1. Installation Testing

```bash
# Package the extension
npm run package

# Install locally
code --install-extension blockchain-security-protocol-1.0.0.vsix

# Verify installation
# - Check Extensions view in VSCode
# - Look for "Blockchain Security Protocol" extension
```

### 2. Extension Activation

- **Open a blockchain project** with `.sol`, `.js`, or `.ts` files
- **Check Command Palette** (`Ctrl+Shift+P`) for SPT commands:
  - `SPT: Scan Project for Security Issues`
  - `SPT: Scan Current File`
  - `SPT: Show Security Report`
  - `SPT: Open Security Dashboard`
  - `SPT: Configure SPT Settings`

### 3. Configuration Testing

```bash
# Open VSCode settings
# Search for "spt"
# Verify all configuration options are available:
```

- `spt.enabled` (boolean, default: true)
- `spt.serverUrl` (string, default: "http://localhost:8080")
- `spt.apiKey` (string, default: "")
- `spt.autoScan` (boolean, default: true)
- `spt.scanOnOpen` (boolean, default: false)
- `spt.chains` (array, default: ["ethereum", "bitcoin", "general"])
- `spt.severity` (string, default: "medium")
- `spt.showInlineDecorations` (boolean, default: true)
- `spt.showProblems` (boolean, default: true)
- `spt.enableCodeLens` (boolean, default: true)
- `spt.enableHover` (boolean, default: true)

### 4. Language Support Testing

Test with different file types:

- **Solidity files** (`.sol`) - Should activate extension
- **JavaScript files** (`.js`) - Should provide security analysis
- **TypeScript files** (`.ts`) - Should provide security analysis
- **Python files** (`.py`) - Should provide security analysis
- **Go files** (`.go`) - Should provide security analysis
- **Rust files** (`.rs`) - Should provide security analysis

### 5. Context Menu Testing

Right-click in editor with supported files:
- **"SPT: Scan Current File"** should appear in context menu
- **"SPT: Fix Security Issue"** should appear when issues are detected

### 6. Backend Integration Testing

**Prerequisites**: SPT backend server running on `http://localhost:8080`

```bash
# Start SPT backend (in separate terminal)
cd ../backend
go run cmd/main.go
```

**Test scenarios**:
- Scan a file with security issues
- Verify communication with backend
- Check error handling when backend is unavailable

### 7. Performance Testing

- **Large projects**: Test with projects containing 100+ files
- **Auto-scan**: Test file save performance with auto-scan enabled
- **Memory usage**: Monitor VSCode memory usage during scanning

## Setting Up Full Test Framework (Optional)

For comprehensive automated testing, you can set up the full Mocha test framework:

### 1. Install Test Dependencies

```bash
npm install --save-dev @types/mocha @types/glob glob mocha
```

### 2. Update TypeScript Configuration

Add to `tsconfig.json`:
```json
{
  "compilerOptions": {
    "types": ["node", "vscode", "mocha"]
  }
}
```

### 3. Enable Full Testing

Update `package.json`:
```json
{
  "scripts": {
    "test": "node ./out/test/runTest.js"
  }
}
```

### 4. Create Comprehensive Tests

Example test file (`src/test/suite/extension.test.ts`):

```typescript
import * as assert from 'assert';
import * as vscode from 'vscode';

suite('Extension Test Suite', () => {
    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('blockchain-spt.blockchain-security-protocol'));
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        assert.ok(commands.includes('spt.scanProject'));
        assert.ok(commands.includes('spt.scanFile'));
    });

    test('Configuration should have default values', () => {
        const config = vscode.workspace.getConfiguration('spt');
        assert.strictEqual(config.get('enabled'), true);
        assert.strictEqual(config.get('serverUrl'), 'http://localhost:8080');
    });
});
```

## Automated Testing with CI/CD

### GitHub Actions Example

```yaml
name: Test VSCode Extension

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd vscode-extension
          npm ci
          
      - name: Run tests
        run: |
          cd vscode-extension
          npm test
          
      - name: Package extension
        run: |
          cd vscode-extension
          npm run package
```

## Troubleshooting Tests

### Common Issues

**"Cannot find module" errors**
```bash
# Clean and reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

**Extension not activating in tests**
```bash
# Check activation events in package.json
# Ensure test workspace contains appropriate files
```

**Backend connection failures**
```bash
# Mock backend responses in tests
# Use test configuration with mock server URL
```

### Debug Mode

Enable debug logging:
```json
{
  "spt.debug": true
}
```

Check VSCode Developer Console:
- `Help` → `Toggle Developer Tools`
- Look for SPT-related logs and errors

## Test Coverage Goals

- **Extension activation**: 100%
- **Command registration**: 100%
- **Configuration handling**: 100%
- **File type detection**: 100%
- **Backend communication**: 80% (with mocking)
- **Error handling**: 90%

## Reporting Issues

When reporting test failures, include:

1. **VSCode version**
2. **Extension version**
3. **Operating system**
4. **Test steps to reproduce**
5. **Expected vs actual behavior**
6. **Console logs** (if available)
7. **Extension logs** (if debug mode enabled)

---

For more information, see:
- [VSCode Extension Testing Guide](https://code.visualstudio.com/api/working-with-extensions/testing-extension)
- [VSCode Extension Development](https://code.visualstudio.com/api)
- [Mocha Testing Framework](https://mochajs.org/)
