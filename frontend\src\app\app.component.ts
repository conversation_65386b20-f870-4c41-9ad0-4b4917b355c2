import { Component, OnInit } from '@angular/core';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDividerModule } from '@angular/material/divider';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { AuthService, User } from './services/auth.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatBadgeModule,
    MatDividerModule
  ],
  template: `
    <!-- Documentation Route - No Authentication Required -->
    <div *ngIf="isDocumentationRoute$ | async" class="doc-container">
      <router-outlet></router-outlet>
    </div>

    <!-- Main App - Authentication Required -->
    <div *ngIf="!(isDocumentationRoute$ | async)" class="app-container">
      <!-- Authenticated Layout -->
      <div *ngIf="currentUser$ | async as user; else loginView" class="authenticated-layout">
        <mat-sidenav-container class="sidenav-container">
          <mat-sidenav #drawer
                       class="sidenav"
                       fixedInViewport
                       [attr.role]="'navigation'"
                       [mode]="isMobile ? 'over' : 'side'"
                       [opened]="!isMobile"
                       [disableClose]="!isMobile">

            <!-- Sidenav Header -->
            <div class="sidenav-header">
              <div class="brand">
                <mat-icon class="brand-icon">shield</mat-icon>
                <span class="brand-text">SPT</span>
              </div>
              <button mat-icon-button
                      *ngIf="isMobile"
                      (click)="drawer.close()"
                      class="close-button">
                <mat-icon>close</mat-icon>
              </button>
            </div>

            <!-- Navigation Menu -->
            <mat-nav-list class="nav-list">
              <a mat-list-item
                 routerLink="/dashboard"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">dashboard</mat-icon>
                <span matListItemTitle class="nav-text">Dashboard</span>
              </a>

              <a mat-list-item
                 routerLink="/scan"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">security</mat-icon>
                <span matListItemTitle class="nav-text">Security Scan</span>
              </a>

              <a mat-list-item
                 routerLink="/checklist"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">checklist</mat-icon>
                <span matListItemTitle class="nav-text">Security Checklist</span>
              </a>

              <a mat-list-item
                 routerLink="/reports"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">assessment</mat-icon>
                <span matListItemTitle class="nav-text">Reports</span>
              </a>

              <a mat-list-item
                 routerLink="/projects"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">folder</mat-icon>
                <span matListItemTitle class="nav-text">Projects</span>
              </a>

              <a mat-list-item
                 routerLink="/settings"
                 routerLinkActive="active"
                 class="nav-item"
                 (click)="isMobile && drawer.close()">
                <mat-icon matListItemIcon class="nav-icon">settings</mat-icon>
                <span matListItemTitle class="nav-text">Settings</span>
              </a>
            </mat-nav-list>

            <!-- User Info -->
            <div class="user-info">
              <div class="user-avatar">
                <mat-icon>account_circle</mat-icon>
              </div>
              <div class="user-details">
                <div class="user-name">{{ user.username }}</div>
                <div class="user-role">{{ user.role | titlecase }}</div>
              </div>
            </div>
          </mat-sidenav>

          <!-- Main Content -->
          <mat-sidenav-content class="main-content">
            <!-- Top Toolbar -->
            <mat-toolbar color="primary" class="top-toolbar">
              <button type="button"
                      mat-icon-button
                      (click)="drawer.toggle()"
                      class="menu-button">
                <mat-icon>menu</mat-icon>
              </button>

              <span class="app-title">🛡️ Blockchain Security Protocol Tool</span>
              <span class="spacer"></span>

              <!-- Notifications -->
              <button mat-icon-button class="toolbar-button">
                <mat-icon matBadge="3" matBadgeColor="warn">notifications</mat-icon>
              </button>

              <!-- User Menu -->
              <button mat-icon-button
                      [matMenuTriggerFor]="userMenu"
                      class="toolbar-button">
                <mat-icon>account_circle</mat-icon>
              </button>

              <mat-menu #userMenu="matMenu">
                <button mat-menu-item>
                  <mat-icon>person</mat-icon>
                  <span>Profile</span>
                </button>
                <button mat-menu-item>
                  <mat-icon>settings</mat-icon>
                  <span>Settings</span>
                </button>
                <mat-divider></mat-divider>
                <button mat-menu-item (click)="logout()">
                  <mat-icon>logout</mat-icon>
                  <span>Logout</span>
                </button>
              </mat-menu>
            </mat-toolbar>

            <!-- Page Content -->
            <div class="page-content">
              <router-outlet></router-outlet>
            </div>
          </mat-sidenav-content>
        </mat-sidenav-container>
      </div>

      <!-- Login View -->
      <ng-template #loginView>
        <div class="login-layout">
          <router-outlet></router-outlet>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    /* Global Layout */
    .app-container {
      height: 100vh;
      overflow: hidden;
    }

    .doc-container {
      height: 100vh;
    }

    .login-layout {
      height: 100vh;
    }

    .authenticated-layout {
      height: 100vh;
    }

    /* Sidenav Container */
    .sidenav-container {
      height: 100vh;
    }

    .sidenav {
      width: 280px;
      background: #ffffff;
      border-right: 1px solid #e0e0e0;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    }

    /* Sidenav Header */
    .sidenav-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      border-bottom: 1px solid #e0e0e0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .brand {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .brand-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .brand-text {
      font-size: 24px;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    .close-button {
      color: white;
    }

    /* Navigation */
    .nav-list {
      padding: 16px 0;
    }

    .nav-item {
      margin: 4px 16px;
      border-radius: 12px;
      transition: all 0.3s ease;
      color: #64748b;
      text-decoration: none;
      min-height: 48px;
    }

    .nav-item:hover {
      background-color: rgba(102, 126, 234, 0.08);
      color: #667eea;
    }

    .nav-item.active {
      background-color: rgba(102, 126, 234, 0.12);
      color: #667eea;
      font-weight: 600;
    }

    .nav-icon {
      color: inherit;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .nav-text {
      font-size: 14px;
      font-weight: 500;
    }

    /* User Info */
    .user-info {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 20px 24px;
      border-top: 1px solid #e0e0e0;
      background: #f8fafc;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-avatar mat-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
      color: #667eea;
    }

    .user-details {
      flex: 1;
    }

    .user-name {
      font-weight: 600;
      font-size: 14px;
      color: #1e293b;
      margin-bottom: 2px;
    }

    .user-role {
      font-size: 12px;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Main Content */
    .main-content {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .top-toolbar {
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .menu-button {
      margin-right: 16px;
    }

    .app-title {
      font-size: 18px;
      font-weight: 600;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .toolbar-button {
      margin-left: 8px;
    }

    /* Page Content */
    .page-content {
      flex: 1;
      overflow-y: auto;
      background: #f8fafc;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .sidenav {
        width: 100%;
        max-width: 320px;
      }

      .app-title {
        font-size: 16px;
      }

      .menu-button {
        margin-right: 8px;
      }

      .user-info {
        padding: 16px 20px;
      }
    }

    /* Material Design Overrides */
    .mat-mdc-list-item {
      --mdc-list-list-item-container-height: 48px;
    }

    .mat-mdc-list-item .mdc-list-item__content {
      padding: 0 16px;
    }

    .mat-toolbar {
      padding: 0 16px;
    }

    .mat-toolbar.mat-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Menu Styling */
    .mat-mdc-menu-panel {
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  `]
})
export class AppComponent implements OnInit {
  title = 'SPT - Blockchain Security Protocol Tool';
  currentUser$: Observable<User | null>;
  isDocumentationRoute$: Observable<boolean>;
  isMobile = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private breakpointObserver: BreakpointObserver
  ) {
    this.currentUser$ = this.authService.currentUser$;

    // Check if current route is documentation - include initial route
    this.isDocumentationRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map((event: NavigationEnd) => event.url.startsWith('/doc')),
      // Start with current URL check
      startWith(this.router.url.startsWith('/doc'))
    );

    // Check for mobile breakpoint
    this.breakpointObserver.observe([Breakpoints.Handset])
      .subscribe(result => {
        this.isMobile = result.matches;
      });
  }

  ngOnInit(): void {
    // Check if user is already logged in
    // Auto-login removed for proper testing
  }

  hasRole(roles: string[]): boolean {
    return this.authService.hasAnyRole(roles);
  }

  logout(): void {
    this.authService.logout();
  }
}
