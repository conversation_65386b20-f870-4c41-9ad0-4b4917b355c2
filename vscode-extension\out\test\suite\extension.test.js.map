{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";AAAA,wBAAwB;AACxB,sEAAsE;;;AAEtE,SAAgB,aAAa;IAC5B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,IAAI;QACH,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;KACZ;IAAC,OAAO,KAAK,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,KAAK,CAAC;KACb;AACF,CAAC;AAhBD,sCAgBC;AAED,2BAA2B;AAC3B,kBAAe,aAAa,CAAC"}