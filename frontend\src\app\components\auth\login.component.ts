import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { AuthService, LoginRequest } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule
  ],
  template: `
    <div class="login-container">
      <!-- Background Pattern -->
      <div class="background-pattern"></div>

      <!-- Header -->
      <header class="app-header">
        <div class="header-content">
          <div class="brand">
            <mat-icon class="brand-icon">shield</mat-icon>
            <span class="brand-text">SPT</span>
          </div>
          <div class="header-actions">
            <button mat-button routerLink="/register" class="header-link">
              Create Account
            </button>
          </div>
        </div>
      </header>

      <div class="login-content">
        <div class="login-card-wrapper">
          <mat-card class="login-card">
            <mat-card-header class="login-header">
              <div class="logo">
                <div class="logo-icon-wrapper">
                  <mat-icon class="logo-icon">security</mat-icon>
                </div>
                <div class="logo-text">
                  <h1>Welcome Back</h1>
                  <p class="subtitle">Sign in to your security dashboard</p>
                </div>
              </div>
            </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
              <div class="form-fields">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Username</mat-label>
                  <input matInput formControlName="username" autocomplete="username">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
                    Username is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Password</mat-label>
                  <input matInput 
                         [type]="hidePassword ? 'password' : 'text'" 
                         formControlName="password"
                         autocomplete="current-password">
                  <button mat-icon-button matSuffix 
                          (click)="hidePassword = !hidePassword" 
                          [attr.aria-label]="'Hide password'" 
                          [attr.aria-pressed]="hidePassword"
                          type="button">
                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                    Password is required
                  </mat-error>
                </mat-form-field>

                <div class="form-options">
                  <mat-checkbox formControlName="rememberMe">
                    Remember me
                  </mat-checkbox>
                  <a href="#" class="forgot-password">Forgot password?</a>
                </div>
              </div>

              <div class="form-actions">
                <button mat-raised-button 
                        color="primary" 
                        type="submit" 
                        class="login-button"
                        [disabled]="loginForm.invalid || isLoading">
                  <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
                  <span *ngIf="!isLoading">Sign In</span>
                </button>

                <button mat-button 
                        type="button" 
                        class="demo-button"
                        (click)="loginWithDemo()"
                        [disabled]="isLoading">
                  Demo Login
                </button>
              </div>
            </form>
          </mat-card-content>

          <mat-card-actions class="card-actions">
            <p>Don't have an account? 
              <a routerLink="/register" class="register-link">Sign up</a>
            </p>
          </mat-card-actions>
        </mat-card>

        <div class="features-info">
          <div class="features-header">
            <mat-icon class="features-icon">verified_user</mat-icon>
            <h3>Security Features</h3>
          </div>
          <div class="features-grid">
            <div class="feature-item">
              <mat-icon>code</mat-icon>
              <div>
                <h4>Smart Contract Analysis</h4>
                <p>Advanced static analysis for Solidity contracts</p>
              </div>
            </div>
            <div class="feature-item">
              <mat-icon>bug_report</mat-icon>
              <div>
                <h4>Vulnerability Detection</h4>
                <p>Identify security flaws and potential exploits</p>
              </div>
            </div>
            <div class="feature-item">
              <mat-icon>monitor</mat-icon>
              <div>
                <h4>Real-time Monitoring</h4>
                <p>Continuous security monitoring and alerts</p>
              </div>
            </div>
            <div class="feature-item">
              <mat-icon>assessment</mat-icon>
              <div>
                <h4>Comprehensive Reports</h4>
                <p>Detailed security reports and recommendations</p>
              </div>
            </div>
          </div>

          <div class="stats">
            <div class="stat-item">
              <span class="stat-number">10K+</span>
              <span class="stat-label">Contracts Analyzed</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">500+</span>
              <span class="stat-label">Vulnerabilities Found</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Accuracy Rate</span>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: relative;
      overflow: hidden;
    }

    .background-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      pointer-events: none;
    }

    .app-header {
      position: relative;
      z-index: 10;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .brand {
      display: flex;
      align-items: center;
      gap: 12px;
      color: white;
    }

    .brand-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .brand-text {
      font-size: 24px;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    .header-link {
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      padding: 8px 16px;
      transition: all 0.2s ease;
    }

    .header-link:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .login-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      position: relative;
      z-index: 1;
    }

    .login-card-wrapper {
      display: flex;
      gap: 60px;
      align-items: center;
      max-width: 1200px;
      width: 100%;
    }

    .login-card {
      flex: 0 0 480px;
      padding: 48px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .login-header {
      margin-bottom: 40px;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 8px;
    }

    .logo-icon-wrapper {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    }

    .logo-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      color: white;
    }

    .logo-text h1 {
      margin: 0;
      color: #1a202c;
      font-weight: 700;
      font-size: 28px;
      letter-spacing: -0.5px;
      line-height: 1.2;
    }

    .subtitle {
      margin: 4px 0 0 0;
      color: #64748b;
      font-size: 16px;
      font-weight: 400;
    }

    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 30px;
    }

    .full-width {
      width: 100%;
    }

    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    .forgot-password {
      color: #667eea;
      text-decoration: none;
      font-size: 14px;
    }

    .forgot-password:hover {
      text-decoration: underline;
    }

    .form-actions {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .login-button {
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .login-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .login-button:hover::before {
      left: 100%;
    }

    .login-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
    }

    .login-button:active {
      transform: translateY(0);
    }

    .demo-button {
      height: 48px;
      color: #667eea;
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: 12px;
      font-weight: 500;
      background: rgba(102, 126, 234, 0.05);
      transition: all 0.3s ease;
    }

    .demo-button:hover {
      background: rgba(102, 126, 234, 0.1);
      border-color: rgba(102, 126, 234, 0.3);
      transform: translateY(-1px);
    }

    .card-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
    }

    .register-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .register-link:hover {
      text-decoration: underline;
    }

    .features-info {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      color: white;
      padding: 48px;
      border-radius: 24px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInRight 0.6s ease-out 0.2s both;
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(30px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .features-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 32px;
    }

    .features-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .features-header h3 {
      font-size: 28px;
      margin: 0;
      font-weight: 700;
    }

    .features-grid {
      display: grid;
      gap: 24px;
      margin-bottom: 40px;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }

    .feature-item:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
    }

    .feature-item mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      margin-top: 2px;
    }

    .feature-item h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .feature-item p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.4;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      padding-top: 32px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      opacity: 0.8;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    @media (max-width: 1024px) {
      .login-card-wrapper {
        flex-direction: column;
        gap: 40px;
        max-width: 600px;
      }

      .login-card {
        flex: none;
        max-width: 100%;
      }

      .features-info {
        order: -1;
      }

      .features-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .stats {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (max-width: 768px) {
      .header-content {
        padding: 12px 16px;
      }

      .brand-text {
        font-size: 20px;
      }

      .login-content {
        padding: 20px 16px;
      }

      .login-card {
        padding: 32px 24px;
      }

      .features-info {
        padding: 32px 24px;
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .feature-item {
        padding: 16px;
      }

      .stats {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .logo {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }

      .logo-text h1 {
        font-size: 24px;
      }
    }

    mat-spinner {
      margin-right: 10px;
    }
  `]
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;
  returnUrl = '/dashboard';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      
      const credentials: LoginRequest = {
        username: this.loginForm.value.username,
        password: this.loginForm.value.password
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
          this.router.navigate([this.returnUrl]);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });
        }
      });
    }
  }

  loginWithDemo(): void {
    this.isLoading = true;
    
    // Use mock login for development
    setTimeout(() => {
      this.authService.mockLogin('admin', 'admin');
      this.isLoading = false;
      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });
      this.router.navigate([this.returnUrl]);
    }, 1000);
  }
}
