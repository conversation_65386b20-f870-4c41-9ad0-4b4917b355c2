package models

import (
	"time"
)

// UserRole represents user roles in the system
type UserRole string

const (
	UserRoleAdmin     UserRole = "admin"
	UserRoleDeveloper UserRole = "developer"
	UserRoleViewer    UserRole = "viewer"
)

// User represents a system user
type User struct {
	ID        string     `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	Username  string     `json:"username" gorm:"size:100;not null;uniqueIndex"`
	Email     string     `json:"email" gorm:"size:255;not null;uniqueIndex"`
	Password  string     `json:"-" gorm:"size:255;not null"` // Hidden from JSON
	FirstName *string    `json:"first_name,omitempty" gorm:"size:100"`
	LastName  *string    `json:"last_name,omitempty" gorm:"size:100"`
	Role      UserRole   `json:"role" gorm:"type:user_role;not null;default:'developer'"`
	IsActive  bool       `json:"is_active" gorm:"default:true"`
	LastLogin *time.Time `json:"last_login,omitempty"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// SetPassword hashes and sets the user password
func (u *User) SetPassword(password string) error {
	// Note: bcrypt import will be added when this method is actually used
	// For now, password hashing is handled in the database seeder
	u.Password = password
	return nil
}

// CheckPassword verifies the user password
func (u *User) CheckPassword(password string) bool {
	// Note: bcrypt import will be added when this method is actually used
	// For now, password verification is handled in the database seeder
	return u.Password == password
}

// ScanStatus represents the status of a security scan
type ScanStatus string

const (
	ScanStatusPending   ScanStatus = "pending"
	ScanStatusRunning   ScanStatus = "running"
	ScanStatusCompleted ScanStatus = "completed"
	ScanStatusFailed    ScanStatus = "failed"
	ScanStatusCancelled ScanStatus = "cancelled"
)

// SecurityIssue represents a security vulnerability or issue
type SecurityIssue struct {
	ID           string            `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	ScanResultID *string           `json:"scan_result_id,omitempty" gorm:"type:uuid;index"`
	Type         string            `json:"type" gorm:"size:100;not null"`                // e.g., "key_leak", "reentrancy", "overflow"
	Severity     string            `json:"severity" gorm:"type:issue_severity;not null"` // critical, high, medium, low, info
	Title        string            `json:"title" gorm:"size:255;not null"`
	Description  string            `json:"description" gorm:"type:text"`
	File         string            `json:"file" gorm:"column:file_path;size:500"`
	Line         int               `json:"line" gorm:"column:line_number"`
	Column       int               `json:"column" gorm:"column:column_number"`
	Code         string            `json:"code" gorm:"column:code_snippet;type:text"`         // The problematic code snippet
	Chain        string            `json:"chain" gorm:"size:50"`                              // ethereum, bitcoin, general
	Category     string            `json:"category" gorm:"size:100"`                          // smart_contract, wallet, environment, dependency
	CWE          string            `json:"cwe" gorm:"column:cwe_id;size:20"`                  // Common Weakness Enumeration ID
	OWASP        string            `json:"owasp" gorm:"size:50"`                              // OWASP category
	References   []string          `json:"references" gorm:"serializer:json"`                 // Links to documentation/fixes
	Suggestion   string            `json:"suggestion" gorm:"column:recommendation;type:text"` // How to fix the issue
	CVSSScore    *float64          `json:"cvss_score,omitempty" gorm:"type:decimal(3,1)"`
	Metadata     map[string]string `json:"metadata" gorm:"serializer:json"` // Additional context
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
}

// ScanResult represents the result of a security scan
type ScanResult struct {
	ID             string                 `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	ProjectPath    string                 `json:"project_path" gorm:"size:500;not null"`
	Chains         []string               `json:"chains" gorm:"type:text[]"`
	Status         ScanStatus             `json:"status" gorm:"type:scan_status;not null;default:'pending'"`
	StartTime      *time.Time             `json:"start_time,omitempty"`
	EndTime        *time.Time             `json:"end_time,omitempty"`
	Duration       *time.Duration         `json:"duration,omitempty" gorm:"type:interval"`
	Issues         []SecurityIssue        `json:"issues" gorm:"foreignKey:ScanResultID"`
	SeverityCounts map[string]int         `json:"severity_counts" gorm:"type:jsonb"`
	FilesScanned   int                    `json:"files_scanned" gorm:"default:0"`
	LinesScanned   int                    `json:"lines_scanned" gorm:"default:0"`
	Error          string                 `json:"error,omitempty" gorm:"column:error_message;type:text"`
	Configuration  map[string]interface{} `json:"configuration" gorm:"type:jsonb"`
	CreatedAt      time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time              `json:"updated_at" gorm:"autoUpdateTime"`
}

// WalletInfo represents wallet-related information
type WalletInfo struct {
	ID             string    `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	Address        string    `json:"address" gorm:"size:255;not null"`
	Chain          string    `json:"chain" gorm:"size:50;not null"`
	Balance        *float64  `json:"balance,omitempty" gorm:"type:decimal(20,8)"`
	IsExposed      bool      `json:"is_exposed" gorm:"default:false"`
	PrivateKeyHash *string   `json:"private_key_hash,omitempty" gorm:"size:64"`
	MnemonicHash   *string   `json:"mnemonic_hash,omitempty" gorm:"size:64"`
	File           string    `json:"file" gorm:"column:file_path;size:500"`
	Line           int       `json:"line" gorm:"column:line_number"`
	ScanResultID   *string   `json:"scan_result_id,omitempty" gorm:"type:uuid;index"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// ContractInfo represents smart contract information
type ContractInfo struct {
	ID                  string    `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	Address             string    `json:"address" gorm:"size:255;not null"`
	Chain               string    `json:"chain" gorm:"size:50;not null"`
	Name                *string   `json:"name,omitempty" gorm:"size:255"`
	SourceCode          *string   `json:"source_code,omitempty" gorm:"type:text"`
	ABI                 *string   `json:"abi,omitempty" gorm:"type:jsonb"`
	Bytecode            *string   `json:"bytecode,omitempty" gorm:"type:text"`
	CompilerVersion     *string   `json:"compiler_version,omitempty" gorm:"size:50"`
	OptimizationEnabled *bool     `json:"optimization_enabled,omitempty"`
	File                string    `json:"file" gorm:"column:file_path;size:500"`
	ScanResultID        *string   `json:"scan_result_id,omitempty" gorm:"type:uuid;index"`
	CreatedAt           time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// DependencyInfo represents dependency information
type DependencyInfo struct {
	ID                   string                 `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	Name                 string                 `json:"name" gorm:"size:255;not null"`
	Version              *string                `json:"version,omitempty" gorm:"size:100"`
	PackageManager       string                 `json:"package_manager" gorm:"size:50"`
	HasVulnerability     bool                   `json:"has_vulnerability" gorm:"default:false"`
	VulnerabilityDetails map[string]interface{} `json:"vulnerability_details,omitempty" gorm:"type:jsonb"`
	File                 string                 `json:"file" gorm:"column:file_path;size:500"`
	ScanResultID         *string                `json:"scan_result_id,omitempty" gorm:"type:uuid;index"`
	CreatedAt            time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt            time.Time              `json:"updated_at" gorm:"autoUpdateTime"`
}

// SecurityReport represents a generated security report
type SecurityReport struct {
	ID           string     `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	ScanResultID string     `json:"scan_result_id" gorm:"type:uuid;not null;index"`
	ScanResult   ScanResult `json:"scan_result" gorm:"foreignKey:ScanResultID"`
	Title        string     `json:"title" gorm:"size:255;not null"`
	Format       string     `json:"format" gorm:"size:50;not null;default:'markdown'"` // markdown, pdf, json
	Content      *string    `json:"content,omitempty" gorm:"type:text"`
	FilePath     *string    `json:"file_path,omitempty" gorm:"size:500"`
	GeneratedAt  time.Time  `json:"generated_at" gorm:"autoCreateTime"`
	CreatedAt    time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// SecurityChecklist represents a security checklist item
type SecurityChecklist struct {
	ID          string    `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	Chain       string    `json:"chain" gorm:"size:50;not null"`     // ethereum, bitcoin, general
	Category    string    `json:"category" gorm:"size:100;not null"` // wallet, contract, environment, deployment
	Title       string    `json:"title" gorm:"size:255;not null"`
	Description *string   `json:"description,omitempty" gorm:"type:text"`
	Priority    int       `json:"priority" gorm:"default:1"`                             // 1=highest, 5=lowest
	Status      string    `json:"status" gorm:"type:checklist_status;default:'pending'"` // pending, in_progress, completed, skipped
	Notes       *string   `json:"notes,omitempty" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// ProjectConfig represents project-specific configuration
type ProjectConfig struct {
	ID                   string                 `json:"id" gorm:"type:uuid;primaryKey;default:uuid_generate_v4()"`
	ProjectPath          string                 `json:"project_path" gorm:"size:500;not null;uniqueIndex"`
	Name                 *string                `json:"name,omitempty" gorm:"size:255"`
	Chains               []string               `json:"chains" gorm:"type:text[];not null"`
	ScanSettings         map[string]interface{} `json:"scan_settings,omitempty" gorm:"type:jsonb"`
	NotificationSettings map[string]interface{} `json:"notification_settings,omitempty" gorm:"type:jsonb"`
	CreatedAt            time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt            time.Time              `json:"updated_at" gorm:"autoUpdateTime"`
}

// ScanRequest represents a scan request
type ScanRequest struct {
	ProjectPath string                 `json:"project_path" binding:"required"`
	Chains      []string               `json:"chains" binding:"required"`
	ScanType    string                 `json:"scan_type"` // full, quick, file
	FilePath    string                 `json:"file_path"` // For file-specific scans
	Options     map[string]interface{} `json:"options"`
}

// ScanResponse represents a scan response
type ScanResponse struct {
	ScanID  string     `json:"scan_id"`
	Status  ScanStatus `json:"status"`
	Message string     `json:"message"`
}

// ReportRequest represents a report generation request
type ReportRequest struct {
	ScanID     string   `json:"scan_id" binding:"required"`
	Format     string   `json:"format"` // markdown, pdf, json
	OutputPath string   `json:"output_path"`
	Sections   []string `json:"sections"`
}

// HealthCheck represents system health status
type HealthCheck struct {
	Status    string            `json:"status"`
	Version   string            `json:"version"`
	Timestamp time.Time         `json:"timestamp"`
	Services  map[string]string `json:"services"`
}
