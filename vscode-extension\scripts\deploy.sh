#!/bin/bash

# VSCode Extension Deployment Script
# This script automates the build, test, and deployment process for the SPT VSCode extension

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
EXTENSION_NAME="blockchain-security-protocol"
PUBLISHER="blockchain-spt"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check vsce
    if ! command -v vsce &> /dev/null; then
        log_warning "vsce is not installed. Installing globally..."
        npm install -g @vscode/vsce
    fi
    
    log_success "Prerequisites check completed"
}

install_dependencies() {
    log_info "Installing dependencies..."
    npm ci
    log_success "Dependencies installed"
}

run_tests() {
    log_info "Running tests and linting..."
    
    # Run linting
    npm run lint
    log_success "Linting passed"
    
    # Run tests
    npm test
    log_success "Tests passed"
}

build_extension() {
    log_info "Building extension..."
    
    # Clean previous build
    rm -rf out/
    
    # Compile TypeScript
    npm run compile
    log_success "Extension built successfully"
}

package_extension() {
    log_info "Packaging extension..."
    
    # Remove old package
    rm -f *.vsix
    
    # Create new package
    npm run package
    
    # Find the created package
    PACKAGE_FILE=$(ls *.vsix | head -n 1)
    if [ -z "$PACKAGE_FILE" ]; then
        log_error "Failed to create package file"
        exit 1
    fi
    
    log_success "Extension packaged as: $PACKAGE_FILE"
}

test_local_install() {
    log_info "Testing local installation..."
    
    # Install the extension locally
    code --install-extension "$PACKAGE_FILE" --force
    log_success "Extension installed locally for testing"
    
    log_warning "Please test the extension in VSCode before publishing"
    read -p "Press Enter to continue with publishing, or Ctrl+C to abort..."
}

publish_extension() {
    local publish_type="$1"
    
    case "$publish_type" in
        "marketplace")
            log_info "Publishing to VS Code Marketplace..."
            
            # Check if logged in
            if ! vsce ls-publishers &> /dev/null; then
                log_warning "Not logged in to marketplace. Please login:"
                vsce login "$PUBLISHER"
            fi
            
            # Publish to marketplace
            npm run publish
            log_success "Extension published to VS Code Marketplace"
            ;;
            
        "pre-release")
            log_info "Publishing pre-release to VS Code Marketplace..."
            vsce publish --pre-release
            log_success "Pre-release extension published to VS Code Marketplace"
            ;;
            
        "package-only")
            log_info "Package created successfully. Skipping publication."
            log_info "You can manually distribute the .vsix file: $PACKAGE_FILE"
            ;;
            
        *)
            log_error "Invalid publish type: $publish_type"
            log_info "Valid options: marketplace, pre-release, package-only"
            exit 1
            ;;
    esac
}

show_usage() {
    echo "Usage: $0 [OPTIONS] [PUBLISH_TYPE]"
    echo ""
    echo "PUBLISH_TYPE:"
    echo "  marketplace    - Publish to VS Code Marketplace (default)"
    echo "  pre-release    - Publish as pre-release version"
    echo "  package-only   - Only create package, don't publish"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help     - Show this help message"
    echo "  --skip-tests   - Skip running tests"
    echo "  --skip-install - Skip local installation test"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build, test, and publish to marketplace"
    echo "  $0 pre-release       # Publish as pre-release"
    echo "  $0 package-only      # Only create package"
    echo "  $0 --skip-tests marketplace  # Skip tests and publish"
}

# Main execution
main() {
    local publish_type="marketplace"
    local skip_tests=false
    local skip_install=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --skip-install)
                skip_install=true
                shift
                ;;
            marketplace|pre-release|package-only)
                publish_type="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting deployment process for $EXTENSION_NAME"
    log_info "Publish type: $publish_type"
    
    # Execute deployment steps
    check_prerequisites
    install_dependencies
    
    if [ "$skip_tests" = false ]; then
        run_tests
    else
        log_warning "Skipping tests as requested"
    fi
    
    build_extension
    package_extension
    
    if [ "$skip_install" = false ] && [ "$publish_type" != "package-only" ]; then
        test_local_install
    fi
    
    publish_extension "$publish_type"
    
    log_success "Deployment completed successfully!"
    
    if [ "$publish_type" != "package-only" ]; then
        log_info "Extension should be available on the marketplace shortly"
        log_info "Monitor the marketplace for publication status"
    fi
}

# Run main function with all arguments
main "$@"
