# VSCode Extension Deployment Scripts

This directory contains automated deployment scripts for the Blockchain Security Protocol (SPT) VSCode extension.

## Scripts

### `deploy.sh` (Linux/macOS)
Bash script for Unix-based systems that automates the complete deployment process.

**Usage:**
```bash
# Make executable (first time only)
chmod +x scripts/deploy.sh

# Deploy to marketplace
./scripts/deploy.sh

# Deploy as pre-release
./scripts/deploy.sh pre-release

# Only create package (no publishing)
./scripts/deploy.sh package-only

# Skip tests and deploy
./scripts/deploy.sh --skip-tests marketplace
```

### `deploy.bat` (Windows)
Batch script for Windows systems with the same functionality as the bash version.

**Usage:**
```cmd
REM Deploy to marketplace
scripts\deploy.bat

REM Deploy as pre-release
scripts\deploy.bat pre-release

REM Only create package (no publishing)
scripts\deploy.bat package-only

REM Skip tests and deploy
scripts\deploy.bat --skip-tests marketplace
```

## What the Scripts Do

1. **Prerequisites Check**: Verify Node.js, npm, and vsce are installed
2. **Dependency Installation**: Install all required npm packages
3. **Testing**: Run linting and unit tests (unless skipped)
4. **Building**: Compile TypeScript to JavaScript
5. **Packaging**: Create a .vsix package file
6. **Local Testing**: Install the extension locally for testing (optional)
7. **Publishing**: Deploy to VS Code Marketplace or create package only

## Options

- `--skip-tests`: Skip running tests and linting
- `--skip-install`: Skip local installation test
- `-h, --help`: Show usage information

## Publish Types

- `marketplace` (default): Publish to VS Code Marketplace
- `pre-release`: Publish as a pre-release version
- `package-only`: Create package but don't publish

## Prerequisites

Before running the scripts, ensure you have:

1. **Node.js 18+** installed
2. **npm** package manager
3. **Visual Studio Code Extension Manager (vsce)** - will be installed automatically if missing
4. **Publisher account** on VS Code Marketplace (for publishing)
5. **Personal Access Token** configured for vsce (for publishing)

## Setup for Publishing

1. **Create Publisher Account**: Visit [Visual Studio Marketplace](https://marketplace.visualstudio.com/manage)
2. **Generate PAT**: Create a Personal Access Token in Azure DevOps
3. **Login to vsce**: Run `vsce login <publisher-name>` and enter your PAT

## Examples

```bash
# Complete deployment workflow
./scripts/deploy.sh

# Quick package creation for testing
./scripts/deploy.sh package-only

# Deploy pre-release version
./scripts/deploy.sh pre-release

# Fast deployment (skip tests)
./scripts/deploy.sh --skip-tests --skip-install marketplace
```

## Troubleshooting

**Script fails with permission error (Linux/macOS):**
```bash
chmod +x scripts/deploy.sh
```

**vsce not found:**
The script will automatically install vsce globally if it's not found.

**Authentication errors:**
```bash
vsce logout
vsce login <your-publisher-name>
```

**Package size too large:**
Check `.vscodeignore` file to exclude unnecessary files from the package.

For more detailed information, see the [VSCode Extension Deployment Guide](../../docs/vscode-extension-deployment.md).
