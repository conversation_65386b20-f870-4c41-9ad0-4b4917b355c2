package api

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	Token     string      `json:"token"`
	User      models.User `json:"user"`
	ExpiresIn int64       `json:"expires_in"`
}

// RefreshRequest represents a token refresh request
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UserStorage interface for user operations
type UserStorage interface {
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	CreateUser(user *models.User) error
	UpdateUser(user *models.User) error
	UserExists(username string) (bool, error)
}

// Login handles user authentication
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Authentication service unavailable",
		})
		return
	}

	// Find user by username
	user, err := userStorage.GetUserByUsername(req.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.WithError(err).Warnf("Invalid password for user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate authentication token (simple implementation)
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate authentication token",
		})
		return
	}

	// Update last login time
	now := time.Now()
	user.LastLogin = &now
	if err := userStorage.UpdateUser(user); err != nil {
		h.logger.WithError(err).Warn("Failed to update last login time")
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, AuthResponse{
		Token:     token,
		User:      *user,
		ExpiresIn: 3600, // 1 hour
	})
}

// Register handles user registration
func (h *Handler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration service unavailable",
		})
		return
	}

	// Check if username already exists
	exists, err := userStorage.UserExists(req.Username)
	if err != nil {
		h.logger.WithError(err).Error("Failed to check if user exists")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	if exists {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Username already exists",
		})
		return
	}

	// Check if email already exists
	if _, err := userStorage.GetUserByEmail(req.Email); err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Email already registered",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Create new user
	user := &models.User{
		Username:  req.Username,
		Email:     req.Email,
		Password:  string(hashedPassword),
		FirstName: stringPtr(req.FirstName),
		LastName:  stringPtr(req.LastName),
		Role:      models.UserRoleDeveloper, // Default role
		IsActive:  true,
	}

	if err := userStorage.CreateUser(user); err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration successful but failed to generate token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusCreated, AuthResponse{
		Token:     token,
		User:      *user,
		ExpiresIn: 3600, // 1 hour
	})
}

// Logout handles user logout
func (h *Handler) Logout(c *gin.Context) {
	// In a real implementation, you would invalidate the token
	// For now, we'll just return success
	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

// RefreshToken handles token refresh
func (h *Handler) RefreshToken(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// In a real implementation, you would validate the refresh token
	// and generate a new access token
	c.JSON(http.StatusNotImplemented, gin.H{
		"error": "Token refresh not implemented yet",
	})
}

// GetCurrentUser returns the current authenticated user
func (h *Handler) GetCurrentUser(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Not authenticated",
		})
		return
	}

	// For now, return a mock user based on the user_id
	// In a real implementation, fetch from database
	c.JSON(http.StatusOK, gin.H{
		"user": gin.H{
			"id":       userID,
			"username": "current_user",
			"role":     "developer",
		},
	})
}

// generateAuthToken generates a simple authentication token
func (h *Handler) generateAuthToken(user *models.User) (string, error) {
	// Generate a random token (in production, use JWT or similar)
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	
	token := hex.EncodeToString(bytes)
	
	// In a real implementation, store this token with expiration in database
	// For now, we'll just return the token
	return fmt.Sprintf("spt_%s_%s", user.Username, token), nil
}

// Helper function
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
