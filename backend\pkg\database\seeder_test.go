package database

import (
	"os"
	"testing"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSeedDefaultUser(t *testing.T) {
	// Create test database
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	require.NoError(t, err)
	require.NotNil(t, db)
	defer db.Close()

	// Test seeding when no users exist
	err = db.SeedDefaultUser()
	assert.NoError(t, err)

	// Verify user was created
	users, err := db.GetAllUsers()
	assert.NoError(t, err)
	assert.Len(t, users, 1)

	user := users[0]
	assert.Equal(t, "admin", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, models.UserRoleAdmin, user.Role)
	assert.True(t, user.IsActive)
	assert.NotEmpty(t, user.Password)

	// Test that seeding again doesn't create duplicate users
	err = db.SeedDefaultUser()
	assert.NoError(t, err)

	users, err = db.GetAllUsers()
	assert.NoError(t, err)
	assert.Len(t, users, 1) // Should still be only 1 user
}

func TestSeedDevelopmentUsers(t *testing.T) {
	// Set environment to development
	os.Setenv("SPT_ENVIRONMENT", "development")
	defer os.Unsetenv("SPT_ENVIRONMENT")

	// Create test database
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	require.NoError(t, err)
	require.NotNil(t, db)
	defer db.Close()

	// Test seeding development users
	err = db.SeedDevelopmentUsers()
	assert.NoError(t, err)

	// Verify users were created
	users, err := db.GetAllUsers()
	assert.NoError(t, err)
	assert.Len(t, users, 2)

	// Check that we have developer and viewer users
	usernames := make(map[string]bool)
	for _, user := range users {
		usernames[user.Username] = true
	}

	assert.True(t, usernames["developer"])
	assert.True(t, usernames["viewer"])
}

func TestSeedAll(t *testing.T) {
	// Set environment to development
	os.Setenv("SPT_ENVIRONMENT", "development")
	defer os.Unsetenv("SPT_ENVIRONMENT")

	// Create test database
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	require.NoError(t, err)
	require.NotNil(t, db)
	defer db.Close()

	// Test seeding all users
	err = db.SeedAll()
	assert.NoError(t, err)

	// Verify all users were created
	users, err := db.GetAllUsers()
	assert.NoError(t, err)
	assert.Len(t, users, 3) // admin + developer + viewer

	// Check that we have all expected users
	usernames := make(map[string]models.UserRole)
	for _, user := range users {
		usernames[user.Username] = user.Role
	}

	assert.Equal(t, models.UserRoleAdmin, usernames["admin"])
	assert.Equal(t, models.UserRoleDeveloper, usernames["developer"])
	assert.Equal(t, models.UserRoleViewer, usernames["viewer"])
}

func TestSeedWithCustomEnvironmentVariables(t *testing.T) {
	// Set custom environment variables
	os.Setenv("SPT_DEFAULT_USERNAME", "customadmin")
	os.Setenv("SPT_DEFAULT_EMAIL", "<EMAIL>")
	os.Setenv("SPT_DEFAULT_PASSWORD", "custompassword")
	defer func() {
		os.Unsetenv("SPT_DEFAULT_USERNAME")
		os.Unsetenv("SPT_DEFAULT_EMAIL")
		os.Unsetenv("SPT_DEFAULT_PASSWORD")
	}()

	// Create test database
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	require.NoError(t, err)
	require.NotNil(t, db)
	defer db.Close()

	// Test seeding with custom values
	err = db.SeedDefaultUser()
	assert.NoError(t, err)

	// Verify custom user was created
	user, err := db.GetUserByUsername("customadmin")
	assert.NoError(t, err)
	assert.Equal(t, "customadmin", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, models.UserRoleAdmin, user.Role)
}
