import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTooltipModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <!-- Header Section -->
      <div class="dashboard-header">
        <div class="header-content">
          <h1 class="dashboard-title">
            <mat-icon class="title-icon">dashboard</mat-icon>
            Security Dashboard
          </h1>
          <p class="dashboard-subtitle">Monitor your blockchain security posture</p>
        </div>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/scan" class="action-button">
            <mat-icon>security</mat-icon>
            Start New Scan
          </button>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="stats-grid">
        <mat-card class="stat-card total-scans">
          <div class="stat-card-content">
            <div class="stat-icon-wrapper">
              <mat-icon class="stat-icon">security</mat-icon>
            </div>
            <div class="stat-details">
              <div class="stat-number">{{ stats.totalScans }}</div>
              <div class="stat-label">Total Scans</div>
              <div class="stat-trend positive">+12% this month</div>
            </div>
          </div>
        </mat-card>

        <mat-card class="stat-card critical-issues">
          <div class="stat-card-content">
            <div class="stat-icon-wrapper critical">
              <mat-icon class="stat-icon">error</mat-icon>
            </div>
            <div class="stat-details">
              <div class="stat-number">{{ stats.criticalIssues }}</div>
              <div class="stat-label">Critical Issues</div>
              <div class="stat-trend negative">-5% this week</div>
            </div>
          </div>
        </mat-card>

        <mat-card class="stat-card high-issues">
          <div class="stat-card-content">
            <div class="stat-icon-wrapper high">
              <mat-icon class="stat-icon">warning</mat-icon>
            </div>
            <div class="stat-details">
              <div class="stat-number">{{ stats.highIssues }}</div>
              <div class="stat-label">High Issues</div>
              <div class="stat-trend neutral">No change</div>
            </div>
          </div>
        </mat-card>

        <mat-card class="stat-card medium-issues">
          <div class="stat-card-content">
            <div class="stat-icon-wrapper medium">
              <mat-icon class="stat-icon">info</mat-icon>
            </div>
            <div class="stat-details">
              <div class="stat-number">{{ stats.mediumIssues }}</div>
              <div class="stat-label">Medium Issues</div>
              <div class="stat-trend positive">-8% this week</div>
            </div>
          </div>
        </mat-card>
      </div>

      <!-- Recent Scans -->
      <div class="content-section">
        <mat-card class="recent-scans-card">
          <mat-card-header class="section-header">
            <div class="section-title">
              <mat-icon>history</mat-icon>
              <mat-card-title>Recent Scans</mat-card-title>
            </div>
            <button mat-stroked-button routerLink="/scan" class="secondary-button">
              <mat-icon>add</mat-icon>
              New Scan
            </button>
          </mat-card-header>
          <mat-card-content>
            <div class="table-container" *ngIf="recentScans.length > 0; else noScans">
              <table mat-table [dataSource]="recentScans" class="scans-table">
                <ng-container matColumnDef="id">
                  <th mat-header-cell *matHeaderCellDef>Scan ID</th>
                  <td mat-cell *matCellDef="let scan">
                    <span class="scan-id">{{ scan.id.substring(0, 8) }}...</span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="project">
                  <th mat-header-cell *matHeaderCellDef>Project</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="project-info">
                      <mat-icon class="project-icon">folder</mat-icon>
                      <span>{{ getProjectName(scan.project_path) }}</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="chains">
                  <th mat-header-cell *matHeaderCellDef>Chains</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="chains-container">
                      <mat-chip *ngFor="let chain of scan.chains" class="chain-chip">
                        {{ chain }}
                      </mat-chip>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let scan">
                    <mat-chip [class]="'status-chip status-' + scan.status">
                      <mat-icon class="status-icon">{{ getStatusIcon(scan.status) }}</mat-icon>
                      {{ scan.status | titlecase }}
                    </mat-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="issues">
                  <th mat-header-cell *matHeaderCellDef>Issues</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="issues-count">
                      <span class="count">{{ scan.issues?.length || 0 }}</span>
                      <span class="label">issues</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="date">
                  <th mat-header-cell *matHeaderCellDef>Date</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="date-info">
                      <span class="date">{{ formatDate(scan.created_at) }}</span>
                      <span class="time">{{ formatTime(scan.created_at) }}</span>
                    </div>
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let scan">
                    <div class="action-buttons">
                      <button mat-icon-button [routerLink]="['/scan', scan.id]" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                      <button mat-icon-button matTooltip="Download Report">
                        <mat-icon>download</mat-icon>
                      </button>
                    </div>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="scan-row"></tr>
              </table>
            </div>

            <ng-template #noScans>
              <div class="empty-state">
                <mat-icon class="empty-icon">security</mat-icon>
                <h3>No scans yet</h3>
                <p>Start your first security scan to see results here</p>
                <button mat-raised-button color="primary" routerLink="/scan">
                  <mat-icon>add</mat-icon>
                  Start First Scan
                </button>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Top Issues -->
      <mat-card class="top-issues-card">
        <mat-card-header>
          <mat-card-title>Top Security Issues</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="issues-list">
            <div *ngFor="let issue of topIssues" class="issue-item">
              <div class="issue-severity">
                <mat-chip [style.background-color]="getSeverityColor(issue.severity)">
                  {{ issue.severity }}
                </mat-chip>
              </div>
              <div class="issue-details">
                <h4>{{ issue.title }}</h4>
                <p>{{ issue.description }}</p>
                <small>{{ issue.file }}:{{ issue.line }}</small>
              </div>
              <div class="issue-chain">
                <mat-chip>{{ issue.chain }}</mat-chip>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions -->
      <mat-card class="quick-actions-card">
        <mat-card-header>
          <mat-card-title>Quick Actions</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button color="primary" routerLink="/scan">
              <mat-icon>security</mat-icon>
              Start New Scan
            </button>
            <button mat-raised-button color="accent" routerLink="/checklist">
              <mat-icon>checklist</mat-icon>
              Security Checklist
            </button>
            <button mat-raised-button routerLink="/reports">
              <mat-icon>assessment</mat-icon>
              Generate Report
            </button>
            <button mat-raised-button routerLink="/settings">
              <mat-icon>settings</mat-icon>
              Settings
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 0;
      max-width: 100%;
      margin: 0;
      min-height: 100%;
    }

    .dashboard-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 32px;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .title-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
    }

    .dashboard-subtitle {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .action-button {
      height: 48px;
      padding: 0 24px;
      border-radius: 12px;
      font-weight: 600;
      background: rgba(255, 255, 255, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      transition: all 0.3s ease;
    }

    .action-button:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-2px);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin: 0 32px 40px 32px;
    }

    .stat-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-card-content {
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .stat-icon-wrapper {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f5f9;
    }

    .stat-icon-wrapper.critical {
      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    }

    .stat-icon-wrapper.high {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    }

    .stat-icon-wrapper.medium {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }

    .stat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .critical-issues .stat-icon {
      color: #dc2626;
    }

    .high-issues .stat-icon {
      color: #d97706;
    }

    .medium-issues .stat-icon {
      color: #2563eb;
    }

    .total-scans .stat-icon {
      color: #059669;
    }

    .stat-details {
      flex: 1;
    }

    .stat-number {
      font-size: 36px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4px;
      line-height: 1;
    }

    .stat-label {
      font-size: 14px;
      color: #64748b;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .stat-trend {
      font-size: 12px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 6px;
      display: inline-block;
    }

    .stat-trend.positive {
      background: #dcfce7;
      color: #166534;
    }

    .stat-trend.negative {
      background: #fee2e2;
      color: #991b1b;
    }

    .stat-trend.neutral {
      background: #f1f5f9;
      color: #475569;
    }

    .content-section {
      margin: 0 32px 40px 32px;
    }

    .recent-scans-card,
    .top-issues-card,
    .quick-actions-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 16px 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .section-title mat-icon {
      color: #667eea;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .section-title mat-card-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    .secondary-button {
      border: 2px solid #667eea;
      color: #667eea;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .secondary-button:hover {
      background: #667eea;
      color: white;
    }

    .table-container {
      overflow-x: auto;
      margin: 0 -24px;
    }

    .scans-table {
      width: 100%;
      background: transparent;
    }

    .scans-table th {
      background: #f8fafc;
      color: #64748b;
      font-weight: 600;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 16px 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .scans-table td {
      padding: 16px 24px;
      border-bottom: 1px solid #f1f5f9;
    }

    .scan-row {
      transition: all 0.2s ease;
    }

    .scan-row:hover {
      background: #f8fafc;
    }

    .scan-id {
      font-family: 'Courier New', monospace;
      background: #f1f5f9;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      color: #475569;
    }

    .project-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .project-icon {
      color: #64748b;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .chains-container {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }

    .chain-chip {
      background: #e0e7ff;
      color: #3730a3;
      font-size: 11px;
      font-weight: 500;
      height: 24px;
      border-radius: 6px;
    }

    .status-chip {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      font-weight: 500;
      height: 28px;
      border-radius: 8px;
      padding: 0 12px;
    }

    .status-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .status-completed {
      background: #dcfce7;
      color: #166534;
    }

    .status-running {
      background: #dbeafe;
      color: #1d4ed8;
    }

    .status-failed {
      background: #fee2e2;
      color: #dc2626;
    }

    .status-pending {
      background: #fef3c7;
      color: #d97706;
    }

    .issues-count {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .issues-count .count {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    .issues-count .label {
      font-size: 11px;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .date-info {
      display: flex;
      flex-direction: column;
    }

    .date-info .date {
      font-size: 14px;
      color: #1e293b;
      font-weight: 500;
    }

    .date-info .time {
      font-size: 12px;
      color: #64748b;
    }

    .action-buttons {
      display: flex;
      gap: 4px;
    }

    .action-buttons button {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      color: #64748b;
      transition: all 0.2s ease;
    }

    .action-buttons button:hover {
      background: #f1f5f9;
      color: #667eea;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #64748b;
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #cbd5e1;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #475569;
      font-size: 20px;
      font-weight: 600;
    }

    .empty-state p {
      margin: 0 0 24px 0;
      font-size: 14px;
    }

    .issues-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .issue-item {
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .issue-details {
      flex: 1;
    }

    .issue-details h4 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .issue-details p {
      margin: 0 0 5px 0;
      color: #666;
      font-size: 14px;
    }

    .issue-details small {
      color: #999;
      font-size: 12px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .actions-grid button {
      height: 60px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .actions-grid mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  `]
})
export class DashboardComponent implements OnInit {
  stats = {
    totalScans: 0,
    criticalIssues: 0,
    highIssues: 0,
    mediumIssues: 0
  };

  recentScans: ScanResult[] = [];
  topIssues: SecurityIssue[] = [];
  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Load recent scans
    this.apiService.getScanHistory().subscribe({
      next: (response) => {
        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent
        this.calculateStats(response.scans);
      },
      error: (error) => {
        console.error('Error loading scan history:', error);
        // Use mock data for development
        this.loadMockData();
      }
    });
  }

  loadMockData(): void {
    // Generate mock data for development
    const mockScan = this.apiService.generateMockScanResult();
    this.recentScans = [mockScan];
    this.topIssues = mockScan.issues;
    this.stats = {
      totalScans: 1,
      criticalIssues: mockScan.severity_counts['critical'] || 0,
      highIssues: mockScan.severity_counts['high'] || 0,
      mediumIssues: mockScan.severity_counts['medium'] || 0
    };
  }

  calculateStats(scans: ScanResult[]): void {
    this.stats.totalScans = scans.length;
    
    let criticalTotal = 0;
    let highTotal = 0;
    let mediumTotal = 0;
    let allIssues: SecurityIssue[] = [];

    scans.forEach(scan => {
      criticalTotal += scan.severity_counts?.['critical'] || 0;
      highTotal += scan.severity_counts?.['high'] || 0;
      mediumTotal += scan.severity_counts?.['medium'] || 0;
      allIssues = allIssues.concat(scan.issues || []);
    });

    this.stats.criticalIssues = criticalTotal;
    this.stats.highIssues = highTotal;
    this.stats.mediumIssues = mediumTotal;

    // Get top 5 most severe issues
    this.topIssues = allIssues
      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))
      .slice(0, 5);
  }

  getSeverityWeight(severity: string): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
    return weights[severity as keyof typeof weights] || 0;
  }

  getSeverityColor(severity: string): string {
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getStatusIcon(status: string): string {
    const icons = {
      completed: 'check_circle',
      running: 'hourglass_empty',
      failed: 'error',
      pending: 'schedule'
    };
    return icons[status as keyof typeof icons] || 'help';
  }
}
