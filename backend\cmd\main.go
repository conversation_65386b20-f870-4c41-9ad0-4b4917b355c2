package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"blockchain-spt/backend/pkg/api"
	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/database"
	"blockchain-spt/backend/pkg/scanner"
	"blockchain-spt/backend/pkg/storage"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// Initialize logger
	logrus.SetFormatter(&logrus.JSONFormatter{})
	logrus.SetLevel(logrus.InfoLevel)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logrus.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := database.New(cfg)
	if err != nil {
		logrus.Warnf("Failed to initialize database: %v", err)
		logrus.Info("Continuing without database - using memory storage")
	}

	// Run database seeding if database is connected
	if db != nil && db.IsConnected() {
		if err := db.SeedAll(); err != nil {
			logrus.Warnf("Failed to seed database: %v", err)
		}
	}

	// Initialize scanner engine
	scannerEngine, err := scanner.NewEngine(cfg)
	if err != nil {
		logrus.Fatalf("Failed to initialize scanner engine: %v", err)
	}

	// Set Gin mode
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize router
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{
		"http://localhost:4200", // Angular dev server
		"http://localhost:3000", // Alternative frontend port
		"http://127.0.0.1:4200",
		"http://127.0.0.1:3000",
	}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"}
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	// Create storage adapter
	var storageAdapter api.StorageInterface
	if db != nil {
		storageAdapter = storage.NewDatabaseAdapter(db)
	} else {
		storageAdapter = storage.NewMemoryStorage()
	}

	// Initialize API routes
	apiHandler := api.NewHandler(scannerEngine, storageAdapter, cfg)
	api.SetupRoutes(router, apiHandler)

	// Create HTTP server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logrus.WithFields(logrus.Fields{
			"address": srv.Addr,
		}).Info("Starting HTTP server")

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Print startup information
	printStartupInfo(cfg)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logrus.Info("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.Fatalf("Server forced to shutdown: %v", err)
	}

	logrus.Info("Server exited")
}

func printStartupInfo(cfg *config.Config) {
	fmt.Println()
	fmt.Println("🛡️  Blockchain Security Protocol Tool (SPT) Backend")
	fmt.Println("==================================================")
	fmt.Printf("🌐 Server URL: http://%s:%d\n", cfg.Server.Host, cfg.Server.Port)
	fmt.Printf("📊 API Endpoints: http://%s:%d/api/v1\n", cfg.Server.Host, cfg.Server.Port)
	fmt.Printf("🔍 Health Check: http://%s:%d/health\n", cfg.Server.Host, cfg.Server.Port)
	fmt.Println()
	fmt.Println("🔧 Available Endpoints:")
	fmt.Println("  POST /api/v1/scan/start          - Start security scan")
	fmt.Println("  GET  /api/v1/scan/result/:id     - Get scan results")
	fmt.Println("  GET  /api/v1/scan/history        - Get scan history")
	fmt.Println("  GET  /api/v1/scan/file?file=path - Scan specific file")
	fmt.Println("  POST /api/v1/report/generate     - Generate security report")
	fmt.Println("  GET  /api/v1/checklist           - Get security checklist")
	fmt.Println("  GET  /api/v1/config              - Get configuration")
	fmt.Println("  PUT  /api/v1/config              - Update configuration")
	fmt.Println("  GET  /ws                         - WebSocket for real-time updates")
	fmt.Println()
	fmt.Println("🚀 Supported Blockchains:")
	fmt.Println("  • Ethereum (Solidity contracts)")
	fmt.Println("  • Bitcoin (Scripts and wallets)")
	fmt.Println("  • General security patterns")
	fmt.Println()
	fmt.Println("🔒 Security Features:")
	fmt.Println("  • Reentrancy vulnerability detection")
	fmt.Println("  • Integer overflow/underflow protection")
	fmt.Println("  • Access control validation")
	fmt.Println("  • Gas optimization analysis")
	fmt.Println("  • Private key exposure detection")
	fmt.Println("  • Environment security checks")
	fmt.Println()
	fmt.Printf("📝 Environment: %s\n", cfg.Environment)
	fmt.Printf("🗄️  Database: %s\n", cfg.Database.Type)
	fmt.Println()
	fmt.Println("✅ Server is ready to accept connections!")
	fmt.Println("==================================================")
	fmt.Println()

	// Log the same information for structured logging
	logrus.WithFields(logrus.Fields{
		"server_url":    fmt.Sprintf("http://%s:%d", cfg.Server.Host, cfg.Server.Port),
		"api_endpoints": fmt.Sprintf("http://%s:%d/api/v1", cfg.Server.Host, cfg.Server.Port),
		"health_check":  fmt.Sprintf("http://%s:%d/health", cfg.Server.Host, cfg.Server.Port),
		"environment":   cfg.Environment,
		"database_type": cfg.Database.Type,
		"blockchains":   []string{"ethereum", "bitcoin", "general"},
	}).Info("SPT Backend Server started successfully")
}
