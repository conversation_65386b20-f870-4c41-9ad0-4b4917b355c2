@echo off
REM VSCode Extension Deployment Script for Windows
REM This script automates the build, test, and deployment process for the SPT VSCode extension

setlocal enabledelayedexpansion

REM Configuration
set EXTENSION_NAME=blockchain-security-protocol
set PUBLISHER=blockchain-spt
set SKIP_TESTS=false
set SKIP_INSTALL=false
set PUBLISH_TYPE=marketplace

REM Parse command line arguments
:parse_args
if "%~1"=="" goto start_deployment
if "%~1"=="-h" goto show_usage
if "%~1"=="--help" goto show_usage
if "%~1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto parse_args
)
if "%~1"=="--skip-install" (
    set SKIP_INSTALL=true
    shift
    goto parse_args
)
if "%~1"=="marketplace" (
    set PUBLISH_TYPE=marketplace
    shift
    goto parse_args
)
if "%~1"=="pre-release" (
    set PUBLISH_TYPE=pre-release
    shift
    goto parse_args
)
if "%~1"=="package-only" (
    set PUBLISH_TYPE=package-only
    shift
    goto parse_args
)
echo [ERROR] Unknown option: %~1
goto show_usage

:show_usage
echo Usage: %0 [OPTIONS] [PUBLISH_TYPE]
echo.
echo PUBLISH_TYPE:
echo   marketplace    - Publish to VS Code Marketplace (default)
echo   pre-release    - Publish as pre-release version
echo   package-only   - Only create package, don't publish
echo.
echo OPTIONS:
echo   -h, --help     - Show this help message
echo   --skip-tests   - Skip running tests
echo   --skip-install - Skip local installation test
echo.
echo Examples:
echo   %0                    # Build, test, and publish to marketplace
echo   %0 pre-release       # Publish as pre-release
echo   %0 package-only      # Only create package
echo   %0 --skip-tests marketplace  # Skip tests and publish
goto end

:start_deployment
echo [INFO] Starting deployment process for %EXTENSION_NAME%
echo [INFO] Publish type: %PUBLISH_TYPE%

REM Check prerequisites
echo [INFO] Checking prerequisites...

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    goto error_exit
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm first.
    goto error_exit
)

REM Check vsce
vsce --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] vsce is not installed. Installing globally...
    npm install -g @vscode/vsce
    if errorlevel 1 (
        echo [ERROR] Failed to install vsce
        goto error_exit
    )
)

echo [SUCCESS] Prerequisites check completed

REM Install dependencies
echo [INFO] Installing dependencies...
npm ci
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    goto error_exit
)
echo [SUCCESS] Dependencies installed

REM Run tests (if not skipped)
if "%SKIP_TESTS%"=="false" (
    echo [INFO] Running tests and linting...
    
    REM Run linting
    npm run lint
    if errorlevel 1 (
        echo [ERROR] Linting failed
        goto error_exit
    )
    echo [SUCCESS] Linting passed
    
    REM Run tests
    npm test
    if errorlevel 1 (
        echo [ERROR] Tests failed
        goto error_exit
    )
    echo [SUCCESS] Tests passed
) else (
    echo [WARNING] Skipping tests as requested
)

REM Build extension
echo [INFO] Building extension...

REM Clean previous build
if exist out rmdir /s /q out

REM Compile TypeScript
npm run compile
if errorlevel 1 (
    echo [ERROR] Failed to build extension
    goto error_exit
)
echo [SUCCESS] Extension built successfully

REM Package extension
echo [INFO] Packaging extension...

REM Remove old package
del *.vsix >nul 2>&1

REM Create new package
npm run package
if errorlevel 1 (
    echo [ERROR] Failed to package extension
    goto error_exit
)

REM Find the created package
for %%f in (*.vsix) do set PACKAGE_FILE=%%f
if "%PACKAGE_FILE%"=="" (
    echo [ERROR] Failed to create package file
    goto error_exit
)

echo [SUCCESS] Extension packaged as: %PACKAGE_FILE%

REM Test local install (if not skipped and not package-only)
if "%SKIP_INSTALL%"=="false" if not "%PUBLISH_TYPE%"=="package-only" (
    echo [INFO] Testing local installation...

    REM Install the extension locally in VSCode (not Visual Studio)
    echo [INFO] Installing extension in VSCode...
    code --install-extension "%PACKAGE_FILE%" --force
    if errorlevel 1 (
        echo [WARNING] Failed to install extension locally, but continuing...
        echo [INFO] You can manually install by:
        echo [INFO] 1. Open VSCode
        echo [INFO] 2. Go to Extensions (Ctrl+Shift+X)
        echo [INFO] 3. Click "..." menu and select "Install from VSIX..."
        echo [INFO] 4. Select the .vsix file: %PACKAGE_FILE%
    ) else (
        echo [SUCCESS] Extension installed locally in VSCode for testing
        echo [INFO] Verify installation with: code --list-extensions | findstr blockchain
    )

    echo [WARNING] Please test the extension in VSCode before publishing
    pause
)

REM Publish extension
if "%PUBLISH_TYPE%"=="marketplace" (
    echo [INFO] Publishing to VS Code Marketplace...
    
    REM Publish to marketplace
    npm run publish
    if errorlevel 1 (
        echo [ERROR] Failed to publish to marketplace
        goto error_exit
    )
    echo [SUCCESS] Extension published to VS Code Marketplace
) else if "%PUBLISH_TYPE%"=="pre-release" (
    echo [INFO] Publishing pre-release to VS Code Marketplace...
    vsce publish --pre-release
    if errorlevel 1 (
        echo [ERROR] Failed to publish pre-release
        goto error_exit
    )
    echo [SUCCESS] Pre-release extension published to VS Code Marketplace
) else if "%PUBLISH_TYPE%"=="package-only" (
    echo [INFO] Package created successfully. Skipping publication.
    echo [INFO] You can manually distribute the .vsix file: %PACKAGE_FILE%
) else (
    echo [ERROR] Invalid publish type: %PUBLISH_TYPE%
    goto error_exit
)

echo [SUCCESS] Deployment completed successfully!

if not "%PUBLISH_TYPE%"=="package-only" (
    echo [INFO] Extension should be available on the marketplace shortly
    echo [INFO] Monitor the marketplace for publication status
)

goto end

:error_exit
echo [ERROR] Deployment failed!
exit /b 1

:end
endlocal
