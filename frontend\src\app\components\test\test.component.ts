import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-test',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule],
  template: `
    <div style="padding: 20px;">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Navigation Test Component</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>This is a test component to verify routing is working.</p>
          <p>Current URL: {{ currentUrl }}</p>
          <p>User authenticated: {{ isAuthenticated }}</p>
          <p>Current user: {{ currentUser | json }}</p>
          
          <div style="margin-top: 20px;">
            <button mat-raised-button color="primary" (click)="testLogin()">
              Test Mock Login
            </button>
            <button mat-raised-button (click)="testLogout()" style="margin-left: 10px;">
              Test Logout
            </button>
          </div>
          
          <div style="margin-top: 20px;">
            <h3>Test Navigation:</h3>
            <button mat-button (click)="navigateTo('/dashboard')">Dashboard</button>
            <button mat-button (click)="navigateTo('/scan')">Scan</button>
            <button mat-button (click)="navigateTo('/reports')">Reports</button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `
})
export class TestComponent {
  currentUrl = '';
  isAuthenticated = false;
  currentUser: any = null;

  constructor(
    private router: Router,
    private authService: AuthService
  ) {
    this.currentUrl = this.router.url;
    this.isAuthenticated = this.authService.isAuthenticated();
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.isAuthenticated = this.authService.isAuthenticated();
    });
  }

  testLogin() {
    console.log('Testing mock login...');
    this.authService.mockLogin('admin', 'admin');
    setTimeout(() => {
      this.isAuthenticated = this.authService.isAuthenticated();
      this.authService.currentUser$.subscribe(user => {
        this.currentUser = user;
      });
    }, 100);
  }

  testLogout() {
    console.log('Testing logout...');
    this.authService.logout();
    setTimeout(() => {
      this.isAuthenticated = this.authService.isAuthenticated();
      this.currentUser = null;
    }, 100);
  }

  navigateTo(path: string) {
    console.log('Navigating to:', path);
    this.router.navigate([path]);
  }
}
